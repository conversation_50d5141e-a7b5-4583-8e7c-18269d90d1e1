import { NextRequest, NextResponse } from 'next/server';
import { createElement, getElementsByDashboard } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dashboardId = searchParams.get('dashboardId');

    if (!dashboardId) {
      return NextResponse.json(
        { error: 'Dashboard ID is required' },
        { status: 400 }
      );
    }

    const elements = await getElementsByDashboard(parseInt(dashboardId));
    return NextResponse.json(elements);
  } catch (error) {
    console.error('Error fetching elements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch elements' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const element = await createElement(body);
    return NextResponse.json(element, { status: 201 });
  } catch (error) {
    console.error('Error creating element:', error);
    return NextResponse.json(
      { error: 'Failed to create element' },
      { status: 500 }
    );
  }
}
