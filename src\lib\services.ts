import { executeDashboardQuery, executeReadQuery } from './db';
import { 
  Dashboard, 
  Element, 
  SavedQuery, 
  QueryHistory, 
  CreateDashboardRequest, 
  CreateElementRequest, 
  UpdateElementRequest,
  MetricResult,
  QueryResult
} from './types';

// Dashboard services
export async function getDashboards(): Promise<Dashboard[]> {
  const result = await executeDashboardQuery('SELECT * FROM dashboards ORDER BY created_at');
  return result.rows;
}

export async function getDashboardByIdentifier(identifier: string): Promise<Dashboard | null> {
  const result = await executeDashboardQuery('SELECT * FROM dashboards WHERE identifier = $1', [identifier]);
  return result.rows[0] || null;
}

export async function createDashboard(data: CreateDashboardRequest): Promise<Dashboard> {
  const result = await executeDashboardQuery(
    'INSERT INTO dashboards (identifier, name) VALUES ($1, $2) RETURNING *',
    [data.identifier, data.name]
  );
  return result.rows[0];
}

export async function updateDashboard(id: number, data: Partial<CreateDashboardRequest>): Promise<Dashboard> {
  const fields = [];
  const values = [];
  let paramCount = 1;

  if (data.name) {
    fields.push(`name = $${paramCount++}`);
    values.push(data.name);
  }
  if (data.identifier) {
    fields.push(`identifier = $${paramCount++}`);
    values.push(data.identifier);
  }

  fields.push(`updated_at = CURRENT_TIMESTAMP`);
  values.push(id);

  const query = `UPDATE dashboards SET ${fields.join(', ')} WHERE id = $${paramCount} RETURNING *`;
  const result = await executeDashboardQuery(query, values);
  return result.rows[0];
}

export async function deleteDashboard(id: number): Promise<void> {
  await executeDashboardQuery('DELETE FROM dashboards WHERE id = $1', [id]);
}

// Element services
export async function getElementsByDashboard(dashboardId: number): Promise<Element[]> {
  const result = await executeDashboardQuery(
    'SELECT * FROM elements WHERE dashboard_id = $1 ORDER BY position, created_at',
    [dashboardId]
  );
  return result.rows;
}

export async function getElementById(id: number): Promise<Element | null> {
  const result = await executeDashboardQuery('SELECT * FROM elements WHERE id = $1', [id]);
  return result.rows[0] || null;
}

export async function getElementByIdentifier(identifier: string): Promise<Element | null> {
  const result = await executeDashboardQuery('SELECT * FROM elements WHERE identifier = $1', [identifier]);
  return result.rows[0] || null;
}

export async function createElement(data: CreateElementRequest): Promise<Element> {
  const result = await executeDashboardQuery(
    `INSERT INTO elements (dashboard_id, identifier, name, type, position, query_text, metric_type, cta_type, cta_value) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *`,
    [
      data.dashboard_id,
      data.identifier,
      data.name,
      data.type,
      data.position || 0,
      data.query_text,
      data.metric_type,
      data.cta_type || 'none',
      data.cta_value
    ]
  );
  return result.rows[0];
}

export async function updateElement(data: UpdateElementRequest): Promise<Element> {
  const fields = [];
  const values = [];
  let paramCount = 1;

  if (data.name) {
    fields.push(`name = $${paramCount++}`);
    values.push(data.name);
  }
  if (data.position !== undefined) {
    fields.push(`position = $${paramCount++}`);
    values.push(data.position);
  }
  if (data.query_text !== undefined) {
    fields.push(`query_text = $${paramCount++}`);
    values.push(data.query_text);
  }
  if (data.metric_type) {
    fields.push(`metric_type = $${paramCount++}`);
    values.push(data.metric_type);
  }
  if (data.cta_type) {
    fields.push(`cta_type = $${paramCount++}`);
    values.push(data.cta_type);
  }
  if (data.cta_value !== undefined) {
    fields.push(`cta_value = $${paramCount++}`);
    values.push(data.cta_value);
  }

  fields.push(`updated_at = CURRENT_TIMESTAMP`);
  values.push(data.id);

  const query = `UPDATE elements SET ${fields.join(', ')} WHERE id = $${paramCount} RETURNING *`;
  const result = await executeDashboardQuery(query, values);
  return result.rows[0];
}

export async function deleteElement(id: number): Promise<void> {
  await executeDashboardQuery('DELETE FROM elements WHERE id = $1', [id]);
}

// Query services
export async function executeMetricQuery(element: Element): Promise<MetricResult> {
  if (!element.query_text) {
    return { type: element.metric_type!, data: null, error: 'No query specified' };
  }

  try {
    const result = await executeReadQuery(element.query_text);
    
    switch (element.metric_type) {
      case 'single':
        return {
          type: 'single',
          data: result.rows[0] ? Object.values(result.rows[0])[0] : null
        };
      case 'list':
        return {
          type: 'list',
          data: result.rows[0] || {}
        };
      case 'table':
        return {
          type: 'table',
          data: {
            rows: result.rows,
            columns: result.fields?.map(f => f.name) || []
          }
        };
      default:
        return { type: element.metric_type!, data: null, error: 'Unknown metric type' };
    }
  } catch (error) {
    return { 
      type: element.metric_type!, 
      data: null, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function executeSQLQuery(query: string): Promise<QueryResult> {
  const result = await executeReadQuery(query);
  return {
    rows: result.rows,
    fields: result.fields || [],
    rowCount: result.rowCount || 0
  };
}

// Saved queries
export async function getSavedQueries(): Promise<SavedQuery[]> {
  const result = await executeDashboardQuery('SELECT * FROM saved_queries ORDER BY created_at DESC');
  return result.rows;
}

export async function saveQuery(name: string, queryText: string): Promise<SavedQuery> {
  const result = await executeDashboardQuery(
    'INSERT INTO saved_queries (name, query_text) VALUES ($1, $2) RETURNING *',
    [name, queryText]
  );
  return result.rows[0];
}

export async function deleteSavedQuery(id: number): Promise<void> {
  await executeDashboardQuery('DELETE FROM saved_queries WHERE id = $1', [id]);
}

// Query history
export async function addToQueryHistory(queryText: string): Promise<void> {
  await executeDashboardQuery(
    'INSERT INTO query_history (query_text) VALUES ($1)',
    [queryText]
  );
}

export async function getQueryHistory(limit: number = 50): Promise<QueryHistory[]> {
  const result = await executeDashboardQuery(
    'SELECT * FROM query_history ORDER BY executed_at DESC LIMIT $1',
    [limit]
  );
  return result.rows;
}
