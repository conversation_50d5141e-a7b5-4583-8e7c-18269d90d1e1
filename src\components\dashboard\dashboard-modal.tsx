"use client"

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dashboard } from '@/lib/types';
import { generateId } from '@/lib/utils';

interface DashboardModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboard?: Dashboard | null;
  onSave: (data: { identifier: string; name: string }) => void;
}

export function DashboardModal({ open, onOpenChange, dashboard, onSave }: DashboardModalProps) {
  const [identifier, setIdentifier] = useState(dashboard?.identifier || '');
  const [name, setName] = useState(dashboard?.name || '');
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    if (!identifier || !name) return;
    
    setLoading(true);
    try {
      await onSave({ identifier, name });
      onOpenChange(false);
      setIdentifier('');
      setName('');
    } catch (error) {
      console.error('Error saving dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateIdentifier = () => {
    const id = generateId();
    setIdentifier(id);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {dashboard ? 'Edit Dashboard' : 'Add Dashboard (settings)'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="identifier">Dashboard identifier</Label>
            <Input
              id="identifier"
              placeholder="element-name"
              value={identifier}
              onChange={(e) => setIdentifier(e.target.value)}
            />
            <div className="text-sm text-green-600">
              site.com/dashboard/{identifier || '{dashboard_identifier}'}
            </div>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="name">Dashboard name</Label>
            <Input
              id="name"
              placeholder="Element name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            onClick={handleSave}
            disabled={!identifier || !name || loading}
            className="w-full bg-black text-white hover:bg-gray-800"
          >
            {loading ? 'Saving...' : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
