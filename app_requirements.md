Let's create the admin app that is fully modular based on the following requirements:

# Overview
The admin app we are creating is primarily centered on the SQL DB credentials which has a user with read only role. The admin app has list of customizable dashboards (one can add/delete a dashboard) with rearrangable blocks of elements within the dashboard page. 

# Elements
Elements are display elements of a dashboard which can be rearranged inside a dashboard to customize the dashboard layout. 

## Element properties
Element identifier: Unique identifier to identify/connect to the element
Element name: Name of the element
Element Type: What type of element is this, decides the display representation
Element CTA: What happens when we click on the element

### Element Types 
Elements are of three types:
Section heading text (H1), heading text (H2), Metric block.

#### Section heading text (H1) and heading text (H2) 
These are just text elements that can be added to the dashboard serves as headings and communicative behaviour.

#### Metric block
Metric block is a block where a DB query is executed and the result is displayed. This is a block that can be added to the dashboard. It can be of three types and has the following properties:
- Single metric block displays with Element name and value. Single metric block query returns a single value.
- List metric block displays key value pairs, each as a single metric block. List metric block query returns a single row with multiple columns(each column as key).
- Table metric block displays a table. Table metric block query returns multiple rows with multiple columns.
- Only metric block has element identifier


### Element CTA
Element CTA can be of many types:
- None : No action
- Opens a dashboard page (connected via dashboard identifier) url: /dashboard/{dashboard_identifier}
- Opens a page showing a metric output (connected via element identifier) url: /element/{element_identifier}
- Opens an external URL 
- Calls an API with key-values as parameters & inputs to the API and displays a confirmation


# UI 
Admin app has bottom naigation bar (in small screens) and fixed sidebar (in large screens) for navigation. The bottom navigation bar and sidebar should have the following items:
Dashboards tab
SQL tab
Profile tab

## Dashboards tab
Dashboards tab has a list of dashboards. Each dashboard has a list of elements. Elements can be rearranged within a dashboard. Elements can be added to a dashboard. Elements can be deleted from a dashboard. Elements can be edited within a dashboard. Dahsboards are navigated using tabs and plus icon is used to add a new dashboard page (with name & identifier). Dashboard pages can be reordered as well. 
- Dashboard tab when double clicked opens a modal to edit/copy the dashboard name and identifier. 
And the dashboard can be identified with /dashboard/{dashboard_identifier}
- Dashboard tab also has a refresh icon to refresh all the elements in the dashboard

## SQL tab
Under bottom navigation bar of SQL tab, there are tabs of sql views which user can navigate to. Plus icon is used to create a new SQL view with previously saved or run queries. SQL view page has two ways of inputs. Page displays search tables, write custom query and list of previously run or saved queries.
## First way 
Block with a searchable textfield displaying list of tables present in the DB,  textfield to apply filters and optional limit field to increase/decrease output rows. This runs the query and results are shown as table. Make sure the SQL keywords are properly formatted to avoid conflicts.
## Second way
To write a custom query and run it. This also results in a table. The table in both ways is shown in mobile friendly way with pagination, sorting and filtering by selecting individual columns just like dbeaver table viewer.

Let each view has refresh icon, custom name and delete icon.

# UI requirements
Primarily a Mobile app and should be responsive to work on desktop as well. Should work on both light mode and dark mode. 

# Backend 
Use 