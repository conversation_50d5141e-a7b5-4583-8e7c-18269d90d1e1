"use client"

import { useState, useEffect } from 'react';
import { AppLayout } from '@/components/layout/app-layout';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Plus, RefreshCw, Settings } from 'lucide-react';
import { Dashboard, Element } from '@/lib/types';
import { DashboardModal } from '@/components/dashboard/dashboard-modal';
import { ElementModal } from '@/components/element/element-modal';
import { ElementDisplay } from '@/components/element/element-display';

export default function DashboardsPage() {
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [activeTab, setActiveTab] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [elements, setElements] = useState<Element[]>([]);
  const [showDashboardModal, setShowDashboardModal] = useState(false);
  const [showElementModal, setShowElementModal] = useState(false);
  const [editingDashboard, setEditingDashboard] = useState<Dashboard | null>(null);
  const [editingElement, setEditingElement] = useState<Element | null>(null);

  useEffect(() => {
    fetchDashboards();
  }, []);

  useEffect(() => {
    if (activeTab) {
      const dashboard = dashboards.find(d => d.identifier === activeTab);
      if (dashboard) {
        fetchElements(dashboard.id);
      }
    }
  }, [activeTab, dashboards]);

  const fetchDashboards = async () => {
    try {
      const response = await fetch('/api/dashboards');
      if (response.ok) {
        const data = await response.json();
        setDashboards(data);
        if (data.length > 0 && !activeTab) {
          setActiveTab(data[0].identifier);
        }
      }
    } catch (error) {
      console.error('Error fetching dashboards:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchElements = async (dashboardId: number) => {
    try {
      const response = await fetch(`/api/elements?dashboardId=${dashboardId}`);
      if (response.ok) {
        const data = await response.json();
        setElements(data);
      }
    } catch (error) {
      console.error('Error fetching elements:', error);
    }
  };

  const handleAddDashboard = () => {
    setEditingDashboard(null);
    setShowDashboardModal(true);
  };

  const handleSaveDashboard = async (data: { identifier: string; name: string }) => {
    try {
      const response = await fetch('/api/dashboards', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        await fetchDashboards();
        setActiveTab(data.identifier);
      }
    } catch (error) {
      console.error('Error saving dashboard:', error);
    }
  };

  const handleAddElement = () => {
    setEditingElement(null);
    setShowElementModal(true);
  };

  const handleSaveElement = async (data: any) => {
    try {
      const response = await fetch('/api/elements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const dashboard = dashboards.find(d => d.identifier === activeTab);
        if (dashboard) {
          fetchElements(dashboard.id);
        }
      }
    } catch (error) {
      console.error('Error saving element:', error);
    }
  };

  const handleRefresh = () => {
    fetchDashboards();
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Loading...</div>
        </div>
      </AppLayout>
    );
  }

  const activeDashboard = dashboards.find(d => d.identifier === activeTab);

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Dashboards</h1>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {dashboards.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground mb-4">No dashboards found</p>
            <Button onClick={handleAddDashboard}>
              <Plus className="h-4 w-4 mr-2" />
              Add Dashboard
            </Button>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="flex items-center justify-between mb-4">
              <TabsList className="grid w-auto grid-cols-auto">
                {dashboards.map((dashboard) => (
                  <TabsTrigger key={dashboard.identifier} value={dashboard.identifier}>
                    {dashboard.name}
                  </TabsTrigger>
                ))}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleAddDashboard}
                  className="ml-2"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </TabsList>
            </div>

            {dashboards.map((dashboard) => (
              <TabsContent key={dashboard.identifier} value={dashboard.identifier}>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold">{dashboard.name}</h2>
                    <Button variant="outline" size="sm" onClick={handleAddElement}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add an element
                    </Button>
                  </div>

                  {/* Dashboard elements */}
                  {elements.length === 0 ? (
                    <div className="min-h-[400px] border border-dashed border-border rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <p className="text-muted-foreground mb-4">No elements in this dashboard</p>
                        <Button variant="outline" onClick={handleAddElement}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add an element
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="grid gap-6">
                      {elements.map((element) => (
                        <ElementDisplay
                          key={element.id}
                          element={element}
                          onEdit={(el) => {
                            setEditingElement(el);
                            setShowElementModal(true);
                          }}
                          onDelete={async (el) => {
                            if (confirm('Are you sure you want to delete this element?')) {
                              try {
                                await fetch(`/api/elements/${el.id}`, { method: 'DELETE' });
                                if (activeDashboard) {
                                  fetchElements(activeDashboard.id);
                                }
                              } catch (error) {
                                console.error('Error deleting element:', error);
                              }
                            }
                          }}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}

        {/* Modals */}
        <DashboardModal
          open={showDashboardModal}
          onOpenChange={setShowDashboardModal}
          dashboard={editingDashboard}
          onSave={handleSaveDashboard}
        />

        {activeDashboard && (
          <ElementModal
            open={showElementModal}
            onOpenChange={setShowElementModal}
            element={editingElement}
            dashboardId={activeDashboard.id}
            onSave={handleSaveElement}
          />
        )}
      </div>
    </AppLayout>
  );
}
