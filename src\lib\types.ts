export interface Dashboard {
  id: number;
  identifier: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface Element {
  id: number;
  dashboard_id: number;
  identifier: string;
  name: string;
  type: ElementType;
  position: number;
  query_text?: string;
  metric_type?: MetricType;
  cta_type: CTAType;
  cta_value?: string;
  created_at: string;
  updated_at: string;
}

export type ElementType = 'section_heading' | 'heading' | 'metric_block';
export type MetricType = 'single' | 'list' | 'table';
export type CTAType = 'none' | 'dashboard' | 'element' | 'external' | 'api';

export interface SavedQuery {
  id: number;
  name: string;
  query_text: string;
  created_at: string;
  updated_at: string;
}

export interface QueryHistory {
  id: number;
  query_text: string;
  executed_at: string;
}

export interface QueryResult {
  rows: any[];
  fields: Array<{
    name: string;
    dataTypeID: number;
  }>;
  rowCount: number;
}

export interface TableInfo {
  table_name: string;
}

export interface ColumnInfo {
  column_name: string;
  data_type: string;
}

export interface CreateDashboardRequest {
  identifier: string;
  name: string;
}

export interface CreateElementRequest {
  dashboard_id: number;
  identifier: string;
  name: string;
  type: ElementType;
  position?: number;
  query_text?: string;
  metric_type?: MetricType;
  cta_type?: CTAType;
  cta_value?: string;
}

export interface UpdateElementRequest {
  id: number;
  name?: string;
  position?: number;
  query_text?: string;
  metric_type?: MetricType;
  cta_type?: CTAType;
  cta_value?: string;
}

export interface MetricResult {
  type: MetricType;
  data: any;
  error?: string;
}
