"use client"

import { useState, useEffect } from 'react';
import { Element, MetricResult } from '@/lib/types';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';

interface ElementDisplayProps {
  element: Element;
  onEdit?: (element: Element) => void;
  onDelete?: (element: Element) => void;
}

export function ElementDisplay({ element, onEdit, onDelete }: ElementDisplayProps) {
  const [metricData, setMetricData] = useState<MetricResult | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (element.type === 'metric_block' && element.query_text) {
      fetchMetricData();
    }
  }, [element]);

  const fetchMetricData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/elements/${element.id}/metric`);
      if (response.ok) {
        const data = await response.json();
        setMetricData(data);
      }
    } catch (error) {
      console.error('Error fetching metric data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    switch (element.type) {
      case 'section_heading':
        return <h1 className="text-2xl font-bold text-foreground">{element.name}</h1>;
      
      case 'heading':
        return <h2 className="text-xl font-semibold text-foreground">{element.name}</h2>;
      
      case 'metric_block':
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">{element.name}</h3>
              <Button variant="ghost" size="sm" className="text-muted-foreground">
                ↗
              </Button>
            </div>
            {loading ? (
              <div className="text-muted-foreground">Loading...</div>
            ) : metricData ? (
              renderMetricData(metricData)
            ) : (
              <div className="text-muted-foreground">No data</div>
            )}
          </div>
        );
      
      default:
        return <div>{element.name}</div>;
    }
  };

  const renderMetricData = (data: MetricResult) => {
    switch (data.type) {
      case 'single':
        return (
          <div className="text-3xl font-bold text-foreground">
            {data.data || '0'}
          </div>
        );
      
      case 'list':
        return (
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(data.data || {}).map(([key, value]) => (
              <div key={key} className="space-y-1">
                <div className="text-sm text-muted-foreground">{key}</div>
                <div className="font-semibold">{String(value)}</div>
              </div>
            ))}
          </div>
        );
      
      case 'table':
        const tableData = data.data as { rows: any[]; columns: string[] };
        return (
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  {tableData.columns?.map((column, index) => (
                    <TableHead key={index}>{column}</TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {tableData.rows?.slice(0, 10).map((row, index) => (
                  <TableRow key={index}>
                    {tableData.columns?.map((column, colIndex) => (
                      <TableCell key={colIndex}>
                        {String(row[column] || '')}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {tableData.rows?.length > 10 && (
              <div className="p-2 text-center text-sm text-muted-foreground border-t">
                Showing 10 of {tableData.rows.length} rows
              </div>
            )}
          </div>
        );
      
      default:
        return <div>Unknown metric type</div>;
    }
  };

  return (
    <div className="group relative p-4 border border-border rounded-lg bg-card hover:shadow-sm transition-shadow">
      {/* Edit/Delete buttons - shown on hover */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="flex space-x-1">
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(element)}
              className="h-8 w-8 p-0"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(element)}
              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {renderContent()}
    </div>
  );
}
