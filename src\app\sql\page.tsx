"use client"

import { useState, useEffect } from 'react';
import { AppLayout } from '@/components/layout/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Search, Play, RefreshCw, Edit, Copy } from 'lucide-react';
import { SavedQuery, QueryResult } from '@/lib/types';

export default function SQLPage() {
  const [activeTab, setActiveTab] = useState('tab1');
  const [searchQuery, setSearchQuery] = useState('');
  const [customQuery, setCustomQuery] = useState('');
  const [savedQueries, setSavedQueries] = useState<SavedQuery[]>([]);
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState<'search' | 'custom'>('search');

  useEffect(() => {
    fetchSavedQueries();
  }, []);

  const fetchSavedQueries = async () => {
    try {
      const response = await fetch('/api/sql/queries');
      if (response.ok) {
        const queries = await response.json();
        setSavedQueries(queries);
      }
    } catch (error) {
      console.error('Error fetching saved queries:', error);
    }
  };

  const executeQuery = async (query: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/sql/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query }),
      });

      if (response.ok) {
        const result = await response.json();
        setQueryResult(result);
      } else {
        console.error('Query execution failed');
      }
    } catch (error) {
      console.error('Error executing query:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredQueries = savedQueries.filter(q =>
    q.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    q.query_text.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">SQL</h1>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex items-center justify-between mb-4">
            <TabsList className="grid w-auto grid-cols-auto">
              <TabsTrigger value="tab1">Tab1</TabsTrigger>
              <TabsTrigger value="tab2">Tab2</TabsTrigger>
              <TabsTrigger value="tab3">Tab3</TabsTrigger>
              <TabsTrigger value="tab4">Tab4</TabsTrigger>
              <Button variant="ghost" size="sm" className="ml-2">
                <Plus className="h-4 w-4" />
              </Button>
            </TabsList>
          </div>

          <TabsContent value={activeTab}>
            <div className="space-y-6">
              {/* Action buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  className="flex-1 bg-black text-white hover:bg-gray-800"
                  onClick={() => setMode('search')}
                  variant={mode === 'search' ? 'default' : 'outline'}
                >
                  Search tables
                </Button>
                <Button
                  className="flex-1 bg-black text-white hover:bg-gray-800"
                  onClick={() => setMode('custom')}
                  variant={mode === 'custom' ? 'default' : 'outline'}
                >
                  Use Custom query
                </Button>
              </div>

              {mode === 'custom' && (
                <div className="space-y-4">
                  <Textarea
                    placeholder="Enter your SQL query here..."
                    value={customQuery}
                    onChange={(e) => setCustomQuery(e.target.value)}
                    className="min-h-[120px] font-mono"
                  />
                  <Button
                    onClick={() => executeQuery(customQuery)}
                    disabled={!customQuery || loading}
                    className="bg-black text-white hover:bg-gray-800"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    {loading ? 'Executing...' : 'Execute Query'}
                  </Button>
                </div>
              )}

              {mode === 'search' && (
                <>
                  <div className="text-center text-muted-foreground">or</div>

                  {/* Search section */}
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="search queries or query names"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 bg-muted"
                      />
                    </div>

                    {/* Query list */}
                    <div className="space-y-3">
                      {filteredQueries.map((queryItem) => (
                        <div
                          key={queryItem.id}
                          className="border border-border rounded-lg p-4 cursor-pointer hover:bg-accent transition-colors"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-medium">{queryItem.name}</h3>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => executeQuery(queryItem.query_text)}
                              >
                                <Play className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground font-mono">
                            {queryItem.query_text}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {/* Query results */}
              {queryResult && (
                <div className="border border-border rounded-lg p-4 bg-muted">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm font-medium">
                      Results ({queryResult.rowCount} rows)
                    </span>
                    <Button variant="ghost" size="sm">
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {queryResult.fields?.map((field, index) => (
                            <TableHead key={index}>{field.name}</TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {queryResult.rows?.slice(0, 50).map((row, index) => (
                          <TableRow key={index}>
                            {queryResult.fields?.map((field, fieldIndex) => (
                              <TableCell key={fieldIndex}>
                                {String(row[field.name] || '')}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {queryResult.rows?.length > 50 && (
                    <div className="mt-4 text-center text-sm text-muted-foreground">
                      Showing 50 of {queryResult.rowCount} rows
                    </div>
                  )}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
