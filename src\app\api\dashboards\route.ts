import { NextRequest, NextResponse } from 'next/server';
import { getDashboards, createDashboard } from '@/lib/services';
import { initializeDashboardSchema } from '@/lib/db';

// Initialize schema on first request
let schemaInitialized = false;

async function ensureSchema() {
  if (!schemaInitialized) {
    try {
      await initializeDashboardSchema();
      schemaInitialized = true;
    } catch (error) {
      console.error('Failed to initialize schema:', error);
    }
  }
}

export async function GET() {
  try {
    await ensureSchema();
    const dashboards = await getDashboards();
    return NextResponse.json(dashboards);
  } catch (error) {
    console.error('Error fetching dashboards:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboards' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureSchema();
    const body = await request.json();
    const { identifier, name } = body;

    if (!identifier || !name) {
      return NextResponse.json(
        { error: 'Identifier and name are required' },
        { status: 400 }
      );
    }

    const dashboard = await createDashboard({ identifier, name });
    return NextResponse.json(dashboard, { status: 201 });
  } catch (error) {
    console.error('Error creating dashboard:', error);
    return NextResponse.json(
      { error: 'Failed to create dashboard' },
      { status: 500 }
    );
  }
}
