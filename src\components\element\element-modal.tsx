"use client"

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Element, ElementType, MetricType } from '@/lib/types';
import { generateId } from '@/lib/utils';

interface ElementModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  element?: Element | null;
  dashboardId: number;
  onSave: (data: any) => void;
}

export function ElementModal({ open, onOpenChange, element, dashboardId, onSave }: ElementModalProps) {
  const [identifier, setIdentifier] = useState(element?.identifier || '');
  const [name, setName] = useState(element?.name || '');
  const [type, setType] = useState<ElementType>(element?.type || 'section_heading');
  const [metricType, setMetricType] = useState<MetricType>(element?.metric_type || 'single');
  const [queryText, setQueryText] = useState(element?.query_text || '');
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    if (!identifier || !name) return;
    
    setLoading(true);
    try {
      const data = {
        dashboard_id: dashboardId,
        identifier,
        name,
        type,
        ...(type === 'metric_block' && {
          metric_type: metricType,
          query_text: queryText,
        }),
      };
      
      await onSave(data);
      onOpenChange(false);
      resetForm();
    } catch (error) {
      console.error('Error saving element:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setIdentifier('');
    setName('');
    setType('section_heading');
    setMetricType('single');
    setQueryText('');
  };

  const generateIdentifier = () => {
    const id = generateId();
    setIdentifier(id);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add an element</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="identifier">Element identifier</Label>
            <Input
              id="identifier"
              placeholder="element-name"
              value={identifier}
              onChange={(e) => setIdentifier(e.target.value)}
            />
            <div className="text-sm text-green-600">
              site.com/element/{identifier || '{element_identifier}'}
            </div>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="name">Element name</Label>
            <Input
              id="name"
              placeholder="Element name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label>Element Type</Label>
            <div className="flex flex-wrap gap-2">
              <Button
                type="button"
                variant={type === 'section_heading' ? 'default' : 'outline'}
                onClick={() => setType('section_heading')}
                className={type === 'section_heading' ? 'bg-black text-white' : ''}
              >
                Section heading text
              </Button>
              <Button
                type="button"
                variant={type === 'heading' ? 'default' : 'outline'}
                onClick={() => setType('heading')}
                className={type === 'heading' ? 'bg-black text-white' : ''}
              >
                Heading text
              </Button>
              <Button
                type="button"
                variant={type === 'metric_block' ? 'default' : 'outline'}
                onClick={() => setType('metric_block')}
                className={type === 'metric_block' ? 'bg-black text-white' : ''}
              >
                Metric block
              </Button>
            </div>
          </div>

          {type === 'metric_block' && (
            <>
              <div className="grid gap-2">
                <Label htmlFor="query">Metric block query</Label>
                <Textarea
                  id="query"
                  placeholder="Write a custom query and save it as a metric or table"
                  value={queryText}
                  onChange={(e) => setQueryText(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>

              <div className="grid gap-2">
                <Label>Show metric output as</Label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="metricType"
                      value="single"
                      checked={metricType === 'single'}
                      onChange={(e) => setMetricType(e.target.value as MetricType)}
                    />
                    <span>Single metric</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="metricType"
                      value="list"
                      checked={metricType === 'list'}
                      onChange={(e) => setMetricType(e.target.value as MetricType)}
                    />
                    <span>List of metrics using key value pairs</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="metricType"
                      value="table"
                      checked={metricType === 'table'}
                      onChange={(e) => setMetricType(e.target.value as MetricType)}
                    />
                    <span>Show as rows of a table</span>
                  </label>
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button
            onClick={handleSave}
            disabled={!identifier || !name || loading}
            className="w-full bg-black text-white hover:bg-gray-800"
          >
            {loading ? 'Saving...' : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
