import { Pool } from 'pg';

// Database connection for reading data (main DB)
const readPool = new Pool({
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  user: process.env.ADMIN_DB_USER,
  password: process.env.ADMIN_DB_PASSWORD,
  port: 5432,
  ssl: false,
});

// Database connection for dashboard data (write DB)
const dashboardPool = new Pool({
  connectionString: process.env.DASHBOARD_DB_DSN,
});

export { readPool, dashboardPool };

// Initialize dashboard schema
export async function initializeDashboardSchema() {
  const client = await dashboardPool.connect();
  
  try {
    // Create dashboards table
    await client.query(`
      CREATE TABLE IF NOT EXISTS dashboards (
        id SERIAL PRIMARY KEY,
        identifier VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create elements table
    await client.query(`
      CREATE TABLE IF NOT EXISTS elements (
        id SERIAL PRIMARY KEY,
        dashboard_id INTEGER REFERENCES dashboards(id) ON DELETE CASCADE,
        identifier VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL, -- 'section_heading', 'heading', 'metric_block'
        position INTEGER DEFAULT 0,
        query_text TEXT,
        metric_type VARCHAR(50), -- 'single', 'list', 'table'
        cta_type VARCHAR(50) DEFAULT 'none', -- 'none', 'dashboard', 'element', 'external', 'api'
        cta_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create saved queries table
    await client.query(`
      CREATE TABLE IF NOT EXISTS saved_queries (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        query_text TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create query history table
    await client.query(`
      CREATE TABLE IF NOT EXISTS query_history (
        id SERIAL PRIMARY KEY,
        query_text TEXT NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('Dashboard schema initialized successfully');
  } catch (error) {
    console.error('Error initializing dashboard schema:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Execute query on read database
export async function executeReadQuery(query: string, params: any[] = []) {
  const client = await readPool.connect();
  try {
    const result = await client.query(query, params);
    return result;
  } catch (error) {
    console.error('Error executing read query:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Execute query on dashboard database
export async function executeDashboardQuery(query: string, params: any[] = []) {
  const client = await dashboardPool.connect();
  try {
    const result = await client.query(query, params);
    return result;
  } catch (error) {
    console.error('Error executing dashboard query:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Get list of tables from read database
export async function getTableList(): Promise<string[]> {
  const client = await readPool.connect();
  try {
    const result = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    return result.rows.map(row => row.table_name);
  } catch (error) {
    console.error('Error fetching table list:', error);
    return [];
  } finally {
    client.release();
  }
}

// Get list of tables from read database
export async function getTableList() {
  const query = `
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    ORDER BY table_name
  `;
  const result = await executeReadQuery(query);
  return result.rows.map(row => row.table_name);
}

// Get table columns
export async function getTableColumns(tableName: string) {
  const query = `
    SELECT column_name, data_type 
    FROM information_schema.columns 
    WHERE table_name = $1 AND table_schema = 'public'
    ORDER BY ordinal_position
  `;
  const result = await executeReadQuery(query, [tableName]);
  return result.rows;
}
