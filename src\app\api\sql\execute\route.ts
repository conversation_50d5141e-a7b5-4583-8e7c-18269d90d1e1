import { NextRequest, NextResponse } from 'next/server';
import { executeSQLQuery, addToQueryHistory } from '@/lib/services';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      );
    }

    // Add to query history
    await addToQueryHistory(query);

    // Execute the query
    const result = await executeSQLQuery(query);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error executing SQL query:', error);
    return NextResponse.json(
      { error: 'Failed to execute query' },
      { status: 500 }
    );
  }
}
