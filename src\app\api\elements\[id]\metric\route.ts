import { NextRequest, NextResponse } from 'next/server';
import { getElementById, executeMetricQuery } from '@/lib/services';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const element = await getElementById(id);
    
    if (!element) {
      return NextResponse.json(
        { error: 'Element not found' },
        { status: 404 }
      );
    }

    if (element.type !== 'metric_block') {
      return NextResponse.json(
        { error: 'Element is not a metric block' },
        { status: 400 }
      );
    }

    const result = await executeMetricQuery(element);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error executing metric query:', error);
    return NextResponse.json(
      { error: 'Failed to execute metric query' },
      { status: 500 }
    );
  }
}
