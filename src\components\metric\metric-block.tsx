"use client"

import { useState, useEffect } from 'react';
import { Element, MetricResult } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ExternalLink, RefreshCw } from 'lucide-react';

interface MetricBlockProps {
  element: Element;
  onEdit?: () => void;
}

export function MetricBlock({ element, onEdit }: MetricBlockProps) {
  const [data, setData] = useState<MetricResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (element.query_text) {
      fetchData();
    }
  }, [element]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/elements/${element.id}/metric`);
      if (response.ok) {
        const result = await response.json();
        setData(result);
      } else {
        setError('Failed to fetch data');
      }
    } catch (err) {
      setError('Error fetching data');
      console.error('Error fetching metric data:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderMetricContent = () => {
    if (loading) {
      return <div className="text-muted-foreground">Loading...</div>;
    }

    if (error) {
      return <div className="text-destructive">{error}</div>;
    }

    if (!data) {
      return <div className="text-muted-foreground">No data available</div>;
    }

    switch (element.metric_type) {
      case 'single':
        return (
          <div className="text-3xl font-bold text-foreground">
            {data.data || '0'}
          </div>
        );

      case 'list':
        const listData = data.data as Record<string, any>;
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {Object.entries(listData || {}).map(([key, value]) => (
              <div key={key} className="space-y-1">
                <div className="text-sm text-muted-foreground">{key}</div>
                <div className="text-xl font-semibold">{String(value)}</div>
              </div>
            ))}
          </div>
        );

      case 'table':
        const tableData = data.data as { rows: any[]; columns: string[] };
        if (!tableData.rows || tableData.rows.length === 0) {
          return <div className="text-muted-foreground">No data available</div>;
        }

        return (
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  {tableData.columns?.map((column, index) => (
                    <TableHead key={index} className="bg-blue-50">
                      {column}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {tableData.rows.slice(0, 10).map((row, index) => (
                  <TableRow key={index}>
                    {tableData.columns?.map((column, colIndex) => (
                      <TableCell key={colIndex}>
                        {String(row[column] || '')}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {tableData.rows.length > 10 && (
              <div className="p-3 text-center text-sm text-muted-foreground border-t bg-muted/50">
                Showing 10 of {tableData.rows.length} rows
              </div>
            )}
          </div>
        );

      default:
        return <div className="text-muted-foreground">Unknown metric type</div>;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{element.name}</h3>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
          <Button variant="ghost" size="sm">
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="min-h-[100px]">
        {renderMetricContent()}
      </div>

      {element.query_text && (
        <details className="text-sm">
          <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
            View Query
          </summary>
          <pre className="mt-2 p-3 bg-muted rounded-md font-mono text-xs overflow-x-auto">
            {element.query_text}
          </pre>
        </details>
      )}
    </div>
  );
}
