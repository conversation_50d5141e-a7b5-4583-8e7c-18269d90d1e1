import { NextRequest, NextResponse } from 'next/server';
import { updateElement, deleteElement, getElementById } from '@/lib/services';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const element = await getElementById(id);
    
    if (!element) {
      return NextResponse.json(
        { error: 'Element not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(element);
  } catch (error) {
    console.error('Error fetching element:', error);
    return NextResponse.json(
      { error: 'Failed to fetch element' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const body = await request.json();
    
    const element = await updateElement({ id, ...body });
    return NextResponse.json(element);
  } catch (error) {
    console.error('Error updating element:', error);
    return NextResponse.json(
      { error: 'Failed to update element' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    await deleteElement(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting element:', error);
    return NextResponse.json(
      { error: 'Failed to delete element' },
      { status: 500 }
    );
  }
}
