import { NextRequest, NextResponse } from 'next/server';
import { getSavedQueries, saveQuery, getQueryHistory } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (type === 'history') {
      const history = await getQueryHistory();
      return NextResponse.json(history);
    } else {
      const queries = await getSavedQueries();
      return NextResponse.json(queries);
    }
  } catch (error) {
    console.error('Error fetching queries:', error);
    return NextResponse.json(
      { error: 'Failed to fetch queries' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, query } = await request.json();

    if (!name || !query) {
      return NextResponse.json(
        { error: 'Name and query are required' },
        { status: 400 }
      );
    }

    const savedQuery = await saveQuery(name, query);
    return NextResponse.json(savedQuery, { status: 201 });
  } catch (error) {
    console.error('Error saving query:', error);
    return NextResponse.json(
      { error: 'Failed to save query' },
      { status: 500 }
    );
  }
}
